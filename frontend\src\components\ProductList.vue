<template>
  <div class="product-list">
    <!-- 篩選工具欄 -->
    <div class="filter-toolbar" v-if="showFilter">
      <div class="search-box">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索商品名稱"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
          <template #append>
            <el-button @click="handleSearch">搜索</el-button>
          </template>
        </el-input>
      </div>
      
      <div class="filter-actions">
        <el-button-group>
          <el-button 
            :type="sortType === 'default' ? 'primary' : 'default'"
            @click="setSortType('default')"
          >
            默認排序
          </el-button>
          <el-button 
            :type="sortType === 'price-asc' ? 'primary' : 'default'"
            @click="setSortType('price-asc')"
          >
            價格 ↑
          </el-button>
          <el-button 
            :type="sortType === 'price-desc' ? 'primary' : 'default'"
            @click="setSortType('price-desc')"
          >
            價格 ↓
          </el-button>
          <el-button 
            :type="sortType === 'sales' ? 'primary' : 'default'"
            @click="setSortType('sales')"
          >
            銷量優先
          </el-button>
        </el-button-group>
        
        <div class="filter-tags">
          <el-tag
            :type="showRecommendedOnly ? 'primary' : 'info'"
            :effect="showRecommendedOnly ? 'dark' : 'plain'"
            @click="toggleRecommendedFilter"
            style="cursor: pointer; margin-right: 8px;"
          >
            推薦商品
          </el-tag>
          <el-tag
            :type="showHotOnly ? 'danger' : 'info'"
            :effect="showHotOnly ? 'dark' : 'plain'"
            @click="toggleHotFilter"
            style="cursor: pointer;"
          >
            熱門商品
          </el-tag>
        </div>
        
        <el-button @click="clearFilters" text>
          <el-icon><Delete /></el-icon>
          清除篩選
        </el-button>
      </div>
    </div>
    
    <!-- 商品列表 -->
    <div class="product-grid" v-loading="loading">
      <template v-if="hasProducts">
        <el-card 
          v-for="product in products" 
          :key="product.id" 
          class="product-card"
          shadow="hover"
          @click="handleProductClick(product)"
        >
          <div class="product-image">
            <img
              :src="getProductImageUrl(product)"
              :alt="product.name"
              class="image"
              @error="handleImageError"
              loading="lazy"
            />
            <div class="product-tags">
              <el-tag v-if="product.isRecommended" type="success" size="small">推薦</el-tag>
              <el-tag v-if="product.isHot" type="danger" size="small">熱門</el-tag>
            </div>
          </div>
          <div class="product-info">
            <h3 class="product-name">{{ product.name }}</h3>
            <div class="product-category" v-if="showCategory">
              <el-tag size="small" type="info">{{ getCategoryName(product.categoryId) }}</el-tag>
            </div>
            <div class="product-price">
              <span class="current-price">¥{{ product.price.toFixed(2) }}</span>
              <span class="original-price" v-if="product.originalPrice">¥{{ product.originalPrice.toFixed(2) }}</span>
            </div>
            <div class="product-stats">
              <span class="stock">庫存: {{ product.stock }}</span>
              <span class="sold">已售: {{ product.soldCount }}</span>
            </div>
          </div>
        </el-card>
      </template>
      
      <!-- 空狀態 -->
      <el-empty
        v-if="!loading && !hasProducts"
        description="暫無商品數據"
        :image-size="200"
      >
        <template #description>
          <p>{{ getEmptyStateText() }}</p>
        </template>
        <div class="empty-actions">
          <el-button @click="clearFilters" v-if="hasFilters" type="primary">
            清除篩選條件
          </el-button>
          <el-button @click="refreshProducts" v-else>
            刷新商品列表
          </el-button>
        </div>
      </el-empty>
    </div>
    
    <!-- 分頁 -->
    <div class="pagination-container" v-if="showPagination && hasProducts">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :page-sizes="[12, 24, 36, 48]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalProducts"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Search, Delete } from '@element-plus/icons-vue'
import { useProductStore } from '../stores/product'
import type { Product, ProductCategory } from '../api/product'

// Props
interface Props {
  showFilter?: boolean
  showPagination?: boolean
  showCategory?: boolean
  categoryId?: number | null
  emptyText?: string
  pageSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  showFilter: true,
  showPagination: true,
  showCategory: true,
  categoryId: null,
  emptyText: '暫無商品數據',
  pageSize: 12
})

// Emits
const emit = defineEmits<{
  productSelect: [product: Product]
}>()

// Router
const router = useRouter()

// Store
const productStore = useProductStore()

// 響應式數據
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(props.pageSize)
const sortType = ref('default')

// 計算屬性
const products = computed(() => productStore.products)
const loading = computed(() => productStore.loading)
const hasProducts = computed(() => productStore.hasProducts)
const totalProducts = computed(() => productStore.totalProducts)
const showRecommendedOnly = computed(() => productStore.showRecommendedOnly)
const showHotOnly = computed(() => productStore.showHotOnly)

// 是否有篩選條件
const hasFilters = computed(() => {
  return searchKeyword.value !== '' || 
    productStore.selectedCategoryId !== null ||
    productStore.showRecommendedOnly ||
    productStore.showHotOnly ||
    sortType.value !== 'default'
})

// 獲取分類名稱
const getCategoryName = (categoryId: number): string => {
  const allCategories = productStore.categories
  const category = allCategories.find(c => c.id === categoryId)
  return category ? category.name : '未分類'
}

// 獲取商品圖片URL
const getProductImageUrl = (product: Product): string => {
  if (product.mainImageUrl) {
    return product.mainImageUrl
  }
  // 根據分類提供不同的默認圖片
  return '/placeholder.svg'
}

// 處理圖片加載錯誤
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  if (img.src !== '/placeholder.svg') {
    img.src = '/placeholder.svg'
  }
}

// 獲取空狀態文本
const getEmptyStateText = (): string => {
  if (productStore.selectedCategoryId) {
    const categoryName = getCategoryName(productStore.selectedCategoryId)
    return `該分類「${categoryName}」暫無商品，請嘗試其他分類或清除篩選條件`
  }
  if (searchKeyword.value) {
    return `未找到與「${searchKeyword.value}」相關的商品，請嘗試其他關鍵詞`
  }
  if (hasFilters.value) {
    return '當前篩選條件下暫無商品，請調整篩選條件'
  }
  return '暫無商品數據，請稍後再試'
}

// 刷新商品列表
const refreshProducts = () => {
  loadProducts()
}

// 加載商品數據
const loadProducts = async () => {
  const page = currentPage.value - 1 // 後端分頁從0開始
  const size = pageSize.value

  // 檢查是否需要使用多條件篩選
  const hasFilters = productStore.showRecommendedOnly ||
                    productStore.showHotOnly ||
                    searchKeyword.value ||
                    sortType.value !== 'default'

  if (hasFilters) {
    // 使用統一的多條件篩選API
    const params = {
      name: searchKeyword.value || undefined,
      categoryId: productStore.selectedCategoryId || undefined,
      status: 1, // 只查詢上架商品
      isRecommended: productStore.showRecommendedOnly ? 1 : undefined,
      isHot: productStore.showHotOnly ? 1 : undefined,
      page,
      size
    }

    // 添加排序參數
    if (sortType.value !== 'default') {
      switch (sortType.value) {
        case 'price-asc':
          params.sortBy = 'price'
          params.sortDirection = 'asc'
          break
        case 'price-desc':
          params.sortBy = 'price'
          params.sortDirection = 'desc'
          break
        case 'sales':
          params.sortBy = 'sales'
          params.sortDirection = 'desc'
          break
      }
    }

    await productStore.filterProducts(params, page, size)
  } else {
    // 使用基本的商品列表API（默認排序）
    if (productStore.selectedCategoryId) {
      await productStore.loadProducts(page, size)
    } else {
      await productStore.loadProducts(page, size)
    }
  }
}

// 處理搜索
const handleSearch = () => {
  currentPage.value = 1
  productStore.searchKeyword = searchKeyword.value
  loadProducts()
}

// 設置排序類型
const setSortType = (type: string) => {
  sortType.value = type
  currentPage.value = 1
  loadProducts()
}

// 切換推薦篩選
const toggleRecommendedFilter = () => {
  productStore.toggleRecommendedFilter()
  currentPage.value = 1
  loadProducts()
}

// 切換熱門篩選
const toggleHotFilter = () => {
  productStore.toggleHotFilter()
  currentPage.value = 1
  loadProducts()
}

// 清除篩選條件
const clearFilters = () => {
  searchKeyword.value = ''
  sortType.value = 'default'
  productStore.clearFilters()
  currentPage.value = 1
  loadProducts()
}

// 處理分頁大小變化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  loadProducts()
}

// 處理頁碼變化
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadProducts()
}

// 處理商品點擊
const handleProductClick = (product: Product) => {
  emit('productSelect', product)
  router.push(`/products/${product.id}`)
}

// 監聽分類ID變化
watch(() => props.categoryId, (newCategoryId) => {
  if (newCategoryId !== productStore.selectedCategoryId) {
    productStore.setCategoryFilter(newCategoryId)
    currentPage.value = 1
    loadProducts()
  }
})

// 監聽store中的分類變化
watch(() => productStore.selectedCategoryId, (newCategoryId) => {
  if (newCategoryId !== props.categoryId) {
    currentPage.value = 1
    loadProducts()
  }
})

// 組件掛載時加載數據
onMounted(async () => {
  // 加載分類數據
  if (productStore.categories.length === 0) {
    await productStore.loadLeafCategories()
  }
  
  // 設置初始分類
  if (props.categoryId !== null) {
    productStore.setCategoryFilter(props.categoryId)
  }
  
  // 加載商品數據
  loadProducts()
})
</script>

<style scoped>
.product-list {
  width: 100%;
}

.filter-toolbar {
  margin-bottom: 20px;
  padding: 16px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.search-box {
  margin-bottom: 16px;
}

.filter-actions {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.filter-tags {
  display: flex;
  gap: 8px;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.product-card {
  cursor: pointer;
  transition: all 0.3s;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.12);
}

.product-image {
  position: relative;
  height: 200px;
  overflow: hidden;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.product-image img[src$=".svg"] {
  object-fit: contain;
  padding: 20px;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-tags {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-info {
  padding: 12px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  margin: 0 0 8px;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  line-height: 1.4;
  height: 2.8em;
}

.product-category {
  margin-bottom: 8px;
}

.product-price {
  margin-top: auto;
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.current-price {
  font-size: 18px;
  font-weight: 600;
  color: #f56c6c;
}

.original-price {
  font-size: 14px;
  color: #999;
  text-decoration: line-through;
}

.product-stats {
  display: flex;
  justify-content: space-between;
  margin-top: 8px;
  font-size: 12px;
  color: #999;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.empty-actions {
  margin-top: 16px;
  display: flex;
  gap: 12px;
  justify-content: center;
}
</style>
