<template>
  <div class="product-detail" v-loading="loading">
    <div v-if="product" class="product-content">
      <!-- 商品圖片區域 -->
      <div class="product-images">
        <div class="main-image" @click="openImagePreview">
          <img
            :src="currentImage"
            :alt="product.name"
            class="main-img"
          />
          <div class="image-tags">
            <el-tag v-if="product.isRecommended" type="success">推薦</el-tag>
            <el-tag v-if="product.isHot" type="danger">熱門</el-tag>
          </div>
          <div class="zoom-hint">
            <el-icon><ZoomIn /></el-icon>
            <span>點擊放大</span>
          </div>
        </div>

        <div class="thumbnail-list" v-if="product.images && product.images.length > 1">
          <div
            v-for="image in product.images"
            :key="image.id"
            class="thumbnail"
            :class="{ active: currentImage === image.imageUrl }"
            @click="setCurrentImage(image.imageUrl)"
          >
            <img :src="image.imageUrl" :alt="image.description" />
          </div>
        </div>
      </div>

      <!-- 圖片預覽對話框 -->
      <el-dialog
        v-model="imagePreviewVisible"
        title="商品圖片"
        width="80%"
        center
      >
        <div class="image-preview-container">
          <img :src="currentImage" :alt="product.name" class="preview-image" />
        </div>
      </el-dialog>
      
      <!-- 商品信息區域 -->
      <div class="product-info">
        <div class="product-header">
          <h1 class="product-title">{{ product.name }}</h1>
          <div class="product-meta">
            <el-breadcrumb separator="/">
              <el-breadcrumb-item>商品</el-breadcrumb-item>
              <el-breadcrumb-item v-if="categoryPath">{{ categoryPath }}</el-breadcrumb-item>
              <el-breadcrumb-item>{{ product.name }}</el-breadcrumb-item>
            </el-breadcrumb>
          </div>
        </div>
        
        <div class="price-section">
          <div class="current-price">¥{{ product.price.toFixed(2) }}</div>
          <div class="original-price" v-if="product.originalPrice">
            原價: ¥{{ product.originalPrice.toFixed(2) }}
          </div>
          <div class="discount" v-if="discountPercent">
            {{ discountPercent }}折
          </div>
        </div>
        
        <div class="product-specs">
          <div class="spec-item" v-if="product.brand">
            <span class="label">品牌:</span>
            <span class="value">{{ product.brand }}</span>
          </div>
          <div class="spec-item" v-if="product.model">
            <span class="label">型號:</span>
            <span class="value">{{ product.model }}</span>
          </div>
          <div class="spec-item" v-if="product.weight">
            <span class="label">重量:</span>
            <span class="value">{{ product.weight }}g</span>
          </div>
          <div class="spec-item">
            <span class="label">庫存:</span>
            <span class="value" :class="{ 'low-stock': product.stock < 10 }">
              {{ product.stock }}件
            </span>
          </div>
          <div class="spec-item">
            <span class="label">銷量:</span>
            <span class="value">{{ product.soldCount }}件</span>
          </div>
        </div>
        
        <div class="product-tags" v-if="productTags.length > 0">
          <span class="label">標籤:</span>
          <el-tag
            v-for="tag in productTags"
            :key="tag"
            size="small"
            class="tag-item"
          >
            {{ tag }}
          </el-tag>
        </div>

        <!-- 商品規格選擇 -->
        <div class="product-specs-selector" v-if="productSpecs.length > 0">
          <div v-for="spec in productSpecs" :key="spec.name" class="spec-group">
            <div class="spec-label">{{ spec.name }}:</div>
            <div class="spec-options">
              <el-button
                v-for="option in spec.options"
                :key="option.value"
                :type="selectedSpecs[spec.name] === option.value ? 'primary' : ''"
                size="small"
                class="spec-option"
                @click="selectSpec(spec.name, option.value)"
                :disabled="!option.available"
              >
                {{ option.label }}
                <span v-if="!option.available" class="unavailable-hint">(缺貨)</span>
              </el-button>
            </div>
          </div>
        </div>

        <div class="action-section">
          <div class="quantity-selector">
            <span class="label">數量:</span>
            <div class="quantity-controls">
              <el-button
                :icon="Minus"
                size="large"
                :disabled="quantity <= 1"
                @click="quantity = Math.max(1, quantity - 1)"
              />
              <el-input-number
                v-model="quantity"
                :min="1"
                :max="product.stock"
                size="large"
                controls-position="right"
                class="quantity-input"
              />
              <el-button
                :icon="Plus"
                size="large"
                :disabled="quantity >= product.stock"
                @click="quantity = Math.min(product.stock, quantity + 1)"
              />
            </div>
            <div class="stock-info">
              <span class="stock-text" :class="{ 'low-stock': product.stock < 10 }">
                庫存 {{ product.stock }} 件
              </span>
            </div>
          </div>

          <div class="action-buttons">
            <el-button
              type="primary"
              size="large"
              :disabled="product.stock === 0 || !canPurchase"
              @click="handleBuyNow"
              :loading="buyNowLoading"
              class="buy-now-btn"
            >
              <el-icon><ShoppingCartFull /></el-icon>
              立即購買
            </el-button>
            <el-button
              size="large"
              :disabled="product.stock === 0 || !canPurchase"
              @click="handleAddToCart"
              :loading="addToCartLoading"
              class="add-cart-btn"
            >
              <el-icon><Plus /></el-icon>
              加入購物車
            </el-button>
            <el-button
              :icon="isFavorited ? StarFilled : Star"
              size="large"
              @click="toggleFavorite"
              :loading="favoriteLoading"
              class="favorite-btn"
            >
              {{ isFavorited ? '已收藏' : '收藏' }}
            </el-button>
          </div>

          <!-- 購買提示 -->
          <div class="purchase-hints" v-if="product.stock > 0">
            <div class="hint-item">
              <el-icon><Truck /></el-icon>
              <span>支持7天無理由退換</span>
            </div>
            <div class="hint-item">
              <el-icon><Shield /></el-icon>
              <span>正品保證</span>
            </div>
            <div class="hint-item">
              <el-icon><Clock /></el-icon>
              <span>24小時內發貨</span>
            </div>
          </div>
        </div>
        
        <div class="view-count" v-if="viewCount > 0">
          <el-icon><View /></el-icon>
          <span>{{ viewCount }} 人瀏覽</span>
        </div>
      </div>
    </div>
    
    <!-- 商品詳情描述 -->
    <div class="product-description" v-if="product">
      <el-tabs v-model="activeTab">
        <el-tab-pane label="商品詳情" name="description">
          <div class="description-content">
            <p v-if="product.description">{{ product.description }}</p>
            <p v-else class="no-description">暫無詳細描述</p>
          </div>
        </el-tab-pane>
        
        <el-tab-pane label="規格參數" name="specs">
          <div class="specs-content">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="商品名稱">{{ product.name }}</el-descriptions-item>
              <el-descriptions-item label="商品品牌" v-if="product.brand">{{ product.brand }}</el-descriptions-item>
              <el-descriptions-item label="商品型號" v-if="product.model">{{ product.model }}</el-descriptions-item>
              <el-descriptions-item label="商品重量" v-if="product.weight">{{ product.weight }}g</el-descriptions-item>
              <el-descriptions-item label="上架時間">{{ formatDate(product.createdAt) }}</el-descriptions-item>
              <el-descriptions-item label="更新時間" v-if="product.updatedAt">{{ formatDate(product.updatedAt) }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <!-- 相關商品推薦 -->
    <div class="related-products" v-if="relatedProducts.length > 0">
      <h3>相關商品推薦</h3>
      <div class="related-grid">
        <el-card 
          v-for="relatedProduct in relatedProducts" 
          :key="relatedProduct.id"
          class="related-card"
          shadow="hover"
          @click="$emit('productSelect', relatedProduct)"
        >
          <img 
            :src="relatedProduct.mainImageUrl || '/placeholder.jpg'" 
            :alt="relatedProduct.name"
            class="related-image"
          />
          <div class="related-info">
            <h4>{{ relatedProduct.name }}</h4>
            <div class="related-price">¥{{ relatedProduct.price.toFixed(2) }}</div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Star, StarFilled, View, ZoomIn, Minus, Plus,
  ShoppingCartFull, Truck, Shield, Clock
} from '@element-plus/icons-vue'
import { useProductStore } from '../stores/product'
import { productAPI, productCategoryAPI, type Product } from '../api/product'

// Props
interface Props {
  productId: number
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  productSelect: [product: Product]
  buyNow: [product: Product, quantity: number]
  addToCart: [product: Product, quantity: number]
}>()

// Store
const productStore = useProductStore()

// 響應式數據
const loading = ref(false)
const product = ref<Product | null>(null)
const relatedProducts = ref<Product[]>([])
const currentImage = ref('')
const quantity = ref(1)
const activeTab = ref('description')
const isFavorited = ref(false)
const viewCount = ref(0)
const categoryPath = ref('')

// 新增的響應式數據
const imagePreviewVisible = ref(false)
const selectedSpecs = ref<Record<string, string>>({})
const buyNowLoading = ref(false)
const addToCartLoading = ref(false)
const favoriteLoading = ref(false)

// 計算屬性
const productTags = computed(() => {
  if (!product.value?.tags) return []
  try {
    return JSON.parse(product.value.tags)
  } catch {
    return product.value.tags.split(',').map(tag => tag.trim())
  }
})

const discountPercent = computed(() => {
  if (!product.value?.originalPrice || !product.value?.price) return null
  const discount = (product.value.price / product.value.originalPrice) * 10
  return Math.round(discount * 10) / 10
})

// 商品規格數據（模擬數據，實際項目中應從後端獲取）
const productSpecs = computed(() => {
  if (!product.value) return []

  // 這裡可以根據商品類型返回不同的規格選項
  const specs = []

  // 示例：如果商品有顏色規格
  if (product.value.categoryId === 1) { // 假設分類1是服裝
    specs.push({
      name: '顏色',
      options: [
        { label: '黑色', value: 'black', available: true },
        { label: '白色', value: 'white', available: true },
        { label: '紅色', value: 'red', available: product.value.stock > 5 }
      ]
    })
    specs.push({
      name: '尺寸',
      options: [
        { label: 'S', value: 'S', available: true },
        { label: 'M', value: 'M', available: true },
        { label: 'L', value: 'L', available: product.value.stock > 3 },
        { label: 'XL', value: 'XL', available: product.value.stock > 1 }
      ]
    })
  }

  return specs
})

// 檢查是否可以購買（所有必選規格都已選擇）
const canPurchase = computed(() => {
  if (!product.value || product.value.stock === 0) return false

  // 檢查是否所有規格都已選擇
  for (const spec of productSpecs.value) {
    if (!selectedSpecs.value[spec.name]) {
      return false
    }
  }

  return true
})

// 加載商品詳情
const loadProductDetail = async () => {
  try {
    loading.value = true
    const response = await productAPI.getProductById(props.productId)
    
    if (response.success && response.data) {
      product.value = response.data
      
      // 設置默認圖片
      if (product.value.images && product.value.images.length > 0) {
        const mainImage = product.value.images.find(img => img.isMain === 1)
        currentImage.value = mainImage ? mainImage.imageUrl : product.value.images[0].imageUrl
      } else if (product.value.mainImageUrl) {
        currentImage.value = product.value.mainImageUrl
      }
      
      // 加載相關數據
      await Promise.all([
        loadRelatedProducts(),
        loadCategoryPath(),
        loadViewCount()
      ])
    } else {
      throw new Error(response.message || '獲取商品詳情失敗')
    }
  } catch (error: any) {
    ElMessage.error('獲取商品詳情失敗: ' + error.message)
  } finally {
    loading.value = false
  }
}

// 加載相關商品
const loadRelatedProducts = async () => {
  if (!product.value) return
  
  try {
    const response = await productAPI.getRelatedProducts(product.value.id, 8)
    if (response.success && response.data) {
      relatedProducts.value = response.data
    }
  } catch (error: any) {
    console.warn('獲取相關商品失敗:', error.message)
  }
}

// 加載分類路徑
const loadCategoryPath = async () => {
  if (!product.value?.categoryId) return
  
  try {
    const response = await productCategoryAPI.getCategoryPath(product.value.categoryId)
    if (response.success && response.data) {
      categoryPath.value = response.data
    }
  } catch (error: any) {
    console.warn('獲取分類路徑失敗:', error.message)
  }
}

// 加載瀏覽次數
const loadViewCount = async () => {
  if (!product.value) return
  
  try {
    const response = await productAPI.getViewCount(product.value.id)
    if (response.success && response.data !== undefined) {
      viewCount.value = response.data
    }
  } catch (error: any) {
    console.warn('獲取瀏覽次數失敗:', error.message)
  }
}

// 設置當前圖片
const setCurrentImage = (imageUrl: string) => {
  currentImage.value = imageUrl
}

// 打開圖片預覽
const openImagePreview = () => {
  imagePreviewVisible.value = true
}

// 選擇規格
const selectSpec = (specName: string, value: string) => {
  selectedSpecs.value[specName] = value
}

// 處理立即購買
const handleBuyNow = async () => {
  if (!product.value || !canPurchase.value) return

  buyNowLoading.value = true
  try {
    // 這裡可以添加規格驗證邏輯
    const productWithSpecs = {
      ...product.value,
      selectedSpecs: selectedSpecs.value
    }
    emit('buyNow', productWithSpecs, quantity.value)
    ElMessage.success('正在跳轉到結算頁面...')
  } catch (error) {
    ElMessage.error('操作失敗，請重試')
  } finally {
    buyNowLoading.value = false
  }
}

// 處理加入購物車
const handleAddToCart = async () => {
  if (!product.value || !canPurchase.value) return

  addToCartLoading.value = true
  try {
    const productWithSpecs = {
      ...product.value,
      selectedSpecs: selectedSpecs.value
    }
    emit('addToCart', productWithSpecs, quantity.value)
    ElMessage.success('已加入購物車')
  } catch (error) {
    ElMessage.error('加入購物車失敗，請重試')
  } finally {
    addToCartLoading.value = false
  }
}

// 切換收藏狀態
const toggleFavorite = async () => {
  favoriteLoading.value = true
  try {
    isFavorited.value = !isFavorited.value
    ElMessage.success(isFavorited.value ? '已收藏' : '已取消收藏')
  } catch (error) {
    ElMessage.error('操作失敗，請重試')
  } finally {
    favoriteLoading.value = false
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 監聽商品ID變化
watch(() => props.productId, (newId) => {
  if (newId) {
    loadProductDetail()
  }
}, { immediate: true })

// 組件掛載時加載數據
onMounted(() => {
  if (props.productId) {
    loadProductDetail()
  }
})
</script>

<style scoped>
.product-detail {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.product-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 40px;
}

.product-images {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.main-image {
  position: relative;
  width: 100%;
  height: 400px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  background: #f5f5f5;
  cursor: pointer;
  transition: transform 0.2s;
}

.main-image:hover {
  transform: scale(1.02);
}

.zoom-hint {
  position: absolute;
  bottom: 12px;
  right: 12px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s;
}

.main-image:hover .zoom-hint {
  opacity: 1;
}

.main-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-tags {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.thumbnail-list {
  display: flex;
  gap: 8px;
  overflow-x: auto;
}

.thumbnail {
  width: 80px;
  height: 80px;
  border: 2px solid transparent;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  flex-shrink: 0;
}

.thumbnail.active {
  border-color: #409eff;
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.product-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.product-meta {
  margin-top: 8px;
}

.price-section {
  display: flex;
  align-items: baseline;
  gap: 12px;
}

.current-price {
  font-size: 28px;
  font-weight: 700;
  color: #f56c6c;
}

.original-price {
  font-size: 16px;
  color: #999;
  text-decoration: line-through;
}

.discount {
  background: #f56c6c;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.product-specs {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.spec-item {
  display: flex;
  align-items: center;
}

.spec-item .label {
  width: 80px;
  color: #666;
  font-size: 14px;
}

.spec-item .value {
  color: #333;
  font-size: 14px;
}

.spec-item .value.low-stock {
  color: #f56c6c;
}

.product-tags {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.product-tags .label {
  color: #666;
  font-size: 14px;
}

.tag-item {
  margin-right: 8px;
}

/* 商品規格選擇樣式 */
.product-specs-selector {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 16px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
}

.spec-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.spec-label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.spec-options {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.spec-option {
  min-width: 60px;
  height: 36px;
  border-radius: 4px;
  transition: all 0.2s;
}

.spec-option:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.unavailable-hint {
  font-size: 10px;
  color: #999;
}

.action-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  border: 1px solid #dee2e6;
}

.quantity-selector {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quantity-selector .label {
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantity-input {
  width: 120px;
}

.stock-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stock-text {
  font-size: 14px;
  color: #666;
}

.stock-text.low-stock {
  color: #f56c6c;
  font-weight: 600;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.buy-now-btn {
  flex: 2;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
  transition: all 0.3s;
}

.buy-now-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
}

.add-cart-btn {
  flex: 2;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
  color: white;
  border: none;
  box-shadow: 0 4px 12px rgba(116, 185, 255, 0.3);
  transition: all 0.3s;
}

.add-cart-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(116, 185, 255, 0.4);
}

.favorite-btn {
  flex: 1;
  height: 48px;
  font-size: 14px;
  transition: all 0.3s;
}

.purchase-hints {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 16px;
  background: #f0f8ff;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.hint-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.hint-item .el-icon {
  color: #409eff;
}

.view-count {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #999;
  font-size: 12px;
}

.product-description {
  margin-bottom: 40px;
}

.description-content,
.specs-content {
  padding: 20px 0;
}

.no-description {
  color: #999;
  font-style: italic;
}

.related-products h3 {
  margin-bottom: 20px;
  font-size: 18px;
  color: #333;
}

.related-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.related-card {
  cursor: pointer;
  transition: transform 0.2s;
}

.related-card:hover {
  transform: translateY(-2px);
}

.related-image {
  width: 100%;
  height: 150px;
  object-fit: cover;
}

.related-info {
  padding: 12px;
}

.related-info h4 {
  margin: 0 0 8px;
  font-size: 14px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-price {
  font-size: 16px;
  font-weight: 600;
  color: #f56c6c;
}

/* 圖片預覽樣式 */
.image-preview-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.preview-image {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .product-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .quantity-controls {
    justify-content: center;
  }

  .purchase-hints {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
